{"version": 3, "file": "Textarea.cjs", "sources": ["../../../src/components/Textarea/Textarea.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteBoolean, FlowbiteColors, ThemingProps } from \"../../types\";\nimport { textareaTheme } from \"./theme\";\n\nexport interface TextareaTheme {\n  base: string;\n  colors: TextareaColors;\n  withShadow: FlowbiteBoolean;\n}\n\nexport interface TextareaColors extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface TextareaProps extends Omit<ComponentProps<\"textarea\">, \"color\" | \"ref\">, ThemingProps<TextareaTheme> {\n  color?: DynamicStringEnumKeysOf<TextareaColors>;\n  shadow?: boolean;\n}\n\nexport const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [textareaTheme, provider.theme?.textarea, props.theme],\n    [get(provider.clearTheme, \"textarea\"), props.clearTheme],\n    [get(provider.applyTheme, \"textarea\"), props.applyTheme],\n  );\n\n  const { className, color = \"gray\", shadow, ...restProps } = resolveProps(props, provider.props?.textarea);\n\n  return (\n    <textarea\n      ref={ref}\n      className={twMerge(theme.base, theme.colors[color], theme.withShadow[shadow ? \"on\" : \"off\"], className)}\n      {...restProps}\n    />\n  );\n});\n\nTextarea.displayName = \"Textarea\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "textareaTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC3G,EAAE,uBAAuBO,cAAG;AAC5B,IAAI,UAAU;AACd,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAEA,OAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAEA,OAAK,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AAC7G,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}