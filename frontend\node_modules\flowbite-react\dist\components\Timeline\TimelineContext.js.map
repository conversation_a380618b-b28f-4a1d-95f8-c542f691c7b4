{"version": 3, "file": "TimelineContext.js", "sources": ["../../../src/components/Timeline/TimelineContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TimelineTheme } from \"./Timeline\";\n\nexport interface TimelineContextValue extends ThemingProps<TimelineTheme> {\n  horizontal?: boolean;\n}\n\nexport const TimelineContext = createContext<TimelineContextValue | undefined>(undefined);\n\nexport function useTimelineContext(): TimelineContextValue {\n  const context = useContext(TimelineContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineContext should be used within the TimelineContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,eAAe,GAAG,aAAa,CAAC,MAAM;AAC5C,SAAS,kBAAkB,GAAG;AACrC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC;AAC7F;AACA,EAAE,OAAO,OAAO;AAChB;;;;"}