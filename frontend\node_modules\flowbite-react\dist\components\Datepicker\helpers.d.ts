export declare enum Views {
    Days = 0,
    Months = 1,
    Years = 2,
    Decades = 3
}
export declare enum WeekStart {
    Sunday = 0,
    Monday = 1,
    Tuesday = 2,
    Wednesday = 3,
    Thursday = 4,
    Friday = 5,
    Saturday = 6
}
export declare function isDateInRange(date: Date, minDate?: Date, maxDate?: Date): boolean;
export declare function isDateEqual(date: Date, selectedDate: Date): boolean;
export declare function isMonthEqual(date: Date, selectedDate: Date): boolean;
export declare function getFirstDateInRange(date: Date, minDate?: Date, maxDate?: Date): Date;
export declare function getFirstDayOfTheMonth(date: Date, weekStart: WeekStart): Date;
export declare function getWeekDays(lang: string, weekStart: WeekStart): string[];
export declare function addDays(date: Date, amount: number): Date;
export declare function addMonths(date: Date, amount: number): Date;
export declare function addYears(date: Date, amount: number): Date;
export declare function getFormattedDate(language: string, date: Date, options?: Intl.DateTimeFormatOptions): string;
export declare function startOfYearPeriod(date: Date, years: number): number;
export declare function isDateInDecade(date: Date, startYear: number): boolean;
export declare function isDateRangeInDecade(startDate: Date, endDate: Date, decadeStart: number, decadeEnd: number): boolean;
