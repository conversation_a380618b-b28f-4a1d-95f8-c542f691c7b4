{"version": 3, "file": "theme.js", "sources": ["../../../src/components/Toast/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { ToastTheme } from \"./Toast\";\n\nexport const toastTheme = createTheme<ToastTheme>({\n  root: {\n    base: \"flex w-full max-w-xs items-center rounded-lg bg-white p-4 text-gray-500 shadow dark:bg-gray-800 dark:text-gray-400\",\n    closed: \"opacity-0 ease-out\",\n  },\n  toggle: {\n    base: \"-m-1.5 ml-auto inline-flex h-8 w-8 rounded-lg bg-white p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-900 focus:ring-2 focus:ring-gray-300 dark:bg-gray-800 dark:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-white\",\n    icon: \"h-5 w-5 shrink-0\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,oHAAoH;AAC9H,IAAI,MAAM,EAAE;AACZ,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,oOAAoO;AAC9O,IAAI,IAAI,EAAE;AACV;AACA,CAAC;;;;"}