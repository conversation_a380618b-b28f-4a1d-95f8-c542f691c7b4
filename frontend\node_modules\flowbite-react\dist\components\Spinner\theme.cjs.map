{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/Spinner/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { SpinnerTheme } from \"./Spinner\";\n\nexport const spinnerTheme = createTheme<SpinnerTheme>({\n  base: \"inline animate-spin text-gray-200\",\n  color: {\n    default: \"fill-primary-600\",\n    failure: \"fill-red-600\",\n    gray: \"fill-gray-600\",\n    info: \"fill-cyan-600\",\n    pink: \"fill-pink-600\",\n    purple: \"fill-purple-600\",\n    success: \"fill-green-500\",\n    warning: \"fill-yellow-400\",\n  },\n  light: {\n    off: {\n      base: \"dark:text-gray-600\",\n      color: {\n        default: \"\",\n        failure: \"\",\n        gray: \"dark:fill-gray-300\",\n        info: \"\",\n        pink: \"\",\n        purple: \"\",\n        success: \"\",\n        warning: \"\",\n      },\n    },\n    on: {\n      base: \"\",\n      color: {\n        default: \"\",\n        failure: \"\",\n        gray: \"\",\n        info: \"\",\n        pink: \"\",\n        purple: \"\",\n        success: \"\",\n        warning: \"\",\n      },\n    },\n  },\n  size: {\n    xs: \"h-3 w-3\",\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\",\n    xl: \"h-10 w-10\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,YAAY,GAAGA,uBAAW,CAAC;AACxC,EAAE,IAAI,EAAE,mCAAmC;AAC3C,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE,kBAAkB;AAC/B,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,IAAI,OAAO,EAAE,gBAAgB;AAC7B,IAAI,OAAO,EAAE;AACb,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,GAAG,EAAE;AACT,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,KAAK,EAAE;AACb,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,IAAI,EAAE,oBAAoB;AAClC,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,KAAK,EAAE;AACb,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,EAAE,EAAE;AACR;AACA,CAAC;;;;"}