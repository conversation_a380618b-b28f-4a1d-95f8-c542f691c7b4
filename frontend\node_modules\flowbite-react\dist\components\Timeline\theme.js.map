{"version": 3, "file": "theme.js", "sources": ["../../../src/components/Timeline/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { TimelineTheme } from \"./Timeline\";\n\nexport const timelineTheme = createTheme<TimelineTheme>({\n  root: {\n    direction: {\n      horizontal: \"sm:flex\",\n      vertical: \"relative border-l border-gray-200 dark:border-gray-700\",\n    },\n  },\n  item: {\n    root: {\n      horizontal: \"relative mb-6 sm:mb-0\",\n      vertical: \"mb-10 ml-6\",\n    },\n    content: {\n      root: {\n        base: \"\",\n        horizontal: \"mt-3 sm:pr-8\",\n        vertical: \"\",\n      },\n      body: {\n        base: \"mb-4 text-base font-normal text-gray-500 dark:text-gray-400\",\n      },\n      time: {\n        base: \"mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500\",\n      },\n      title: {\n        base: \"text-lg font-semibold text-gray-900 dark:text-white\",\n      },\n    },\n    point: {\n      horizontal: \"flex items-center\",\n      line: \"hidden h-0.5 w-full bg-gray-200 sm:flex dark:bg-gray-700\",\n      marker: {\n        base: {\n          horizontal:\n            \"absolute -left-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700\",\n          vertical:\n            \"absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700\",\n        },\n        icon: {\n          base: \"h-3 w-3 text-primary-600 dark:text-primary-300\",\n          wrapper:\n            \"absolute -left-3 flex h-6 w-6 items-center justify-center rounded-full bg-primary-200 ring-8 ring-white dark:bg-primary-900 dark:ring-gray-900\",\n        },\n      },\n      vertical: \"\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,aAAa,GAAG,WAAW,CAAC;AACzC,EAAE,IAAI,EAAE;AACR,IAAI,SAAS,EAAE;AACf,MAAM,UAAU,EAAE,SAAS;AAC3B,MAAM,QAAQ,EAAE;AAChB;AACA,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE;AACV,MAAM,UAAU,EAAE,uBAAuB;AACzC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,UAAU,EAAE,cAAc;AAClC,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,IAAI,EAAE,0DAA0D;AACtE,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,UAAU,EAAE,+GAA+G;AACrI,UAAU,QAAQ,EAAE;AACpB,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,gDAAgD;AAChE,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB;AACA;AACA,CAAC;;;;"}