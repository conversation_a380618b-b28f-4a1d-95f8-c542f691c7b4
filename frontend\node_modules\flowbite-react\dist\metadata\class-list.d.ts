export declare const CLASS_LIST_MAP: {
    Accordion: string[];
    Alert: string[];
    Avatar: string[];
    Badge: string[];
    Banner: never[];
    Blockquote: string[];
    Breadcrumb: string[];
    Button: string[];
    Card: string[];
    Carousel: string[];
    Checkbox: string[];
    Clipboard: string[];
    DarkThemeToggle: string[];
    Datepicker: string[];
    Drawer: string[];
    Dropdown: string[];
    FileInput: string[];
    FloatingLabel: string[];
    Footer: string[];
    HR: string[];
    HelperText: string[];
    Kbd: string[];
    Label: string[];
    List: string[];
    ListGroup: string[];
    MegaMenu: string[];
    Modal: string[];
    Navbar: string[];
    Pagination: string[];
    Popover: string[];
    Progress: string[];
    Radio: string[];
    RangeSlider: string[];
    Rating: string[];
    Select: string[];
    Sidebar: string[];
    Spinner: string[];
    Table: string[];
    Tabs: string[];
    TextInput: string[];
    Textarea: string[];
    Timeline: string[];
    Toast: string[];
    ToggleSwitch: string[];
    Tooltip: string[];
};
export declare const COMPONENT_TO_CLASS_LIST_MAP: {
    Accordion: string;
    AccordionContent: string;
    AccordionTitle: string;
    Alert: string;
    Avatar: string;
    AvatarGroup: string;
    AvatarGroupCounter: string;
    Badge: string;
    Banner: string;
    BannerCollapseButton: string;
    Blockquote: string;
    Breadcrumb: string;
    BreadcrumbItem: string;
    Button: string;
    ButtonGroup: string;
    Card: string;
    Carousel: string;
    Checkbox: string;
    Clipboard: string;
    ClipboardWithIcon: string;
    ClipboardWithIconText: string;
    DarkThemeToggle: string;
    Datepicker: string;
    Drawer: string;
    DrawerHeader: string;
    DrawerItems: string;
    Dropdown: string;
    DropdownDivider: string;
    DropdownHeader: string;
    DropdownItem: string;
    FileInput: string;
    FloatingLabel: string;
    Footer: string;
    FooterBrand: string;
    FooterCopyright: string;
    FooterDivider: string;
    FooterIcon: string;
    FooterLink: string;
    FooterLinkGroup: string;
    FooterTitle: string;
    HR: string;
    HRIcon: string;
    HRSquare: string;
    HRText: string;
    HRTrimmed: string;
    HelperText: string;
    Kbd: string;
    Label: string;
    List: string;
    ListItem: string;
    ListGroup: string;
    ListGroupItem: string;
    MegaMenu: string;
    MegaMenuDropdown: string;
    MegaMenuDropdownToggle: string;
    Modal: string;
    ModalBody: string;
    ModalFooter: string;
    ModalHeader: string;
    Navbar: string;
    NavbarBrand: string;
    NavbarCollapse: string;
    NavbarLink: string;
    NavbarToggle: string;
    Pagination: string;
    PaginationButton: string;
    Popover: string;
    Progress: string;
    Radio: string;
    RangeSlider: string;
    Rating: string;
    RatingAdvanced: string;
    RatingStar: string;
    Select: string;
    Sidebar: string;
    SidebarCTA: string;
    SidebarCollapse: string;
    SidebarItem: string;
    SidebarItemGroup: string;
    SidebarItems: string;
    SidebarLogo: string;
    Spinner: string;
    Table: string;
    TableBody: string;
    TableCell: string;
    TableHead: string;
    TableHeadCell: string;
    TableRow: string;
    TabItem: string;
    Tabs: string;
    TextInput: string;
    Textarea: string;
    Timeline: string;
    TimelineBody: string;
    TimelineContent: string;
    TimelineItem: string;
    TimelinePoint: string;
    TimelineTime: string;
    TimelineTitle: string;
    Toast: string;
    ToastToggle: string;
    ToggleSwitch: string;
    Tooltip: string;
};
