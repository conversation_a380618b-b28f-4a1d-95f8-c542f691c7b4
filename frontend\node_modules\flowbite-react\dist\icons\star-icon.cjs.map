{"version": 3, "file": "star-icon.cjs", "sources": ["../../src/icons/star-icon.tsx"], "sourcesContent": ["import { forwardRef, type SVGProps } from \"react\";\n\nexport const StarIcon = forwardRef<SVGSVGElement, SVGProps<SVGSVGElement>>((props, ref) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"1em\"\n    height=\"1em\"\n    fill=\"currentColor\"\n    stroke=\"currentColor\"\n    strokeWidth={0}\n    viewBox=\"0 0 20 20\"\n    ref={ref}\n    {...props}\n  >\n    <path\n      stroke=\"none\"\n      d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 0 0 .95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 0 0-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 0 0-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 0 0-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 0 0 .951-.69l1.07-3.292z\"\n    />\n  </svg>\n));\nStarIcon.displayName = \"StarIcon\";\n"], "names": ["forwardRef", "jsx"], "mappings": ";;;;;AAGY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,qBAAqBC,cAAG;AACtE,EAAE,KAAK;AACP,EAAE;AACF,IAAI,KAAK,EAAE,4BAA4B;AACvC,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,GAAG;AACP,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,kBAAkBA,cAAG;AACjC,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,CAAC,EAAE;AACX;AACA;AACA;AACA,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}