/**
 * Applies a prefix to class names while preserving modifiers and arbitrary values.
 *
 * @param classNames - A string containing one or more CSS class names separated by spaces
 * @param prefix - The prefix to be added to each class name
 * @param separator - The separator used between class name parts (default ":")
 * @returns A new string with the prefix applied to each class name
 */
export declare function applyPrefixV3(classNames: string, prefix: string, separator?: string): string;
