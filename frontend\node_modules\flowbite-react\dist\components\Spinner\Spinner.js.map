{"version": 3, "file": "Spinner.js", "sources": ["../../../src/components/Spinner/Spinner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, FlowbiteSizes, ThemingProps } from \"../../types\";\nimport { spinnerTheme } from \"./theme\";\n\nexport interface SpinnerTheme {\n  base: string;\n  color: SpinnerColors;\n  light: {\n    off: {\n      base: string;\n      color: SpinnerColors;\n    };\n    on: {\n      base: string;\n      color: SpinnerColors;\n    };\n  };\n  size: SpinnerSizes;\n}\n\nexport interface SpinnerColors\n  extends Pick<FlowbiteColors, \"failure\" | \"gray\" | \"info\" | \"pink\" | \"purple\" | \"success\" | \"warning\"> {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface SpinnerSizes extends Pick<FlowbiteSizes, \"xs\" | \"sm\" | \"md\" | \"lg\" | \"xl\"> {\n  [key: string]: string;\n}\n\nexport interface SpinnerProps extends Omit<ComponentProps<\"span\">, \"color\">, ThemingProps<SpinnerTheme> {\n  color?: DynamicStringEnumKeysOf<SpinnerColors>;\n  light?: boolean;\n  size?: DynamicStringEnumKeysOf<SpinnerSizes>;\n}\n\nexport const Spinner = forwardRef<HTMLSpanElement, SpinnerProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [spinnerTheme, provider.theme?.spinner, props.theme],\n    [get(provider.clearTheme, \"spinner\"), props.clearTheme],\n    [get(provider.applyTheme, \"spinner\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    color = \"default\",\n    light,\n    size = \"lg\",\n    ...restProps\n  } = resolveProps(props, provider.props?.spinner);\n\n  return (\n    <span ref={ref} role=\"status\" {...restProps}>\n      <svg\n        fill=\"none\"\n        viewBox=\"0 0 100 101\"\n        className={twMerge(\n          theme.base,\n          theme.color[color],\n          theme.light[light ? \"on\" : \"off\"].base,\n          theme.light[light ? \"on\" : \"off\"].color[color],\n          theme.size[size],\n          className,\n        )}\n      >\n        <path\n          d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n          fill=\"currentColor\"\n        />\n        <path\n          d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n          fill=\"currentFill\"\n        />\n      </svg>\n    </span>\n  );\n});\n\nSpinner.displayName = \"Spinner\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,OAAO,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAClD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC;AACxD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,SAAS;AACrB,IAAI,KAAK;AACT,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AAClD,EAAE,uBAAuB,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,QAAQ,kBAAkB,IAAI;AACxG,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,KAAK,CAAC,IAAI;AAClB,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC1B,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI;AAC9C,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;AACtD,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAQ;AACR,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,wBAAwB,GAAG;AAC3B,UAAU,MAAM;AAChB,UAAU;AACV,YAAY,CAAC,EAAE,8WAA8W;AAC7X,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,wBAAwB,GAAG;AAC3B,UAAU,MAAM;AAChB,UAAU;AACV,YAAY,CAAC,EAAE,+kBAA+kB;AAC9lB,YAAY,IAAI,EAAE;AAClB;AACA;AACA;AACA;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}