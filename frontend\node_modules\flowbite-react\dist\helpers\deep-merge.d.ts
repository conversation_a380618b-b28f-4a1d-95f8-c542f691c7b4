export declare function deepMergeStrings(merge: (values: string[]) => string): <Ts extends readonly unknown[]>(...objects: Ts) => import("deepmerge-ts").DeepMergeHKT<Ts, Readonly<{
    DeepMergeRecordsURI: "DeepMergeRecordsDefaultURI";
    DeepMergeArraysURI: "DeepMergeArraysDefaultURI";
    DeepMergeSetsURI: "DeepMergeSetsDefaultURI";
    DeepMergeMapsURI: "DeepMergeMapsDefaultURI";
    DeepMergeOthersURI: "DeepMergeLeafURI";
    DeepMergeFilterValuesURI: "DeepMergeFilterValuesDefaultURI";
}>, <PERSON>only<{
    key: PropertyK<PERSON>;
    parents: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><Readonly<Record<PropertyKey, unknown>>>;
}>>;
