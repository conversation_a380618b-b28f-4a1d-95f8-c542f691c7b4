{"version": 3, "file": "strip-dark.cjs", "sources": ["../../src/helpers/strip-dark.ts"], "sourcesContent": ["const cache = new Map<string, string>();\n\n/**\n * Removes all dark mode classes from a string of class names.\n *\n * This function filters out any class that starts with \"dark:\" prefix\n * from the provided string of space-separated class names.\n * Results are cached for performance.\n *\n * @param {string} classNames - A string containing one or more CSS class names separated by spaces\n * @returns {string} A new string with all dark mode classes removed\n *\n * @example\n * ```ts\n * stripDark(\"text-black dark:text-white bg-white dark:bg-black\"); // \"text-black bg-white\"\n * ```\n */\nexport function stripDark(classNames: string): string {\n  if (classNames === undefined || classNames === null) {\n    return classNames;\n  }\n\n  if (!classNames.trim().length) {\n    return classNames;\n  }\n\n  classNames = classNames.trim();\n\n  const cacheKey = classNames;\n  const cacheValue = cache.get(cacheKey);\n\n  if (cacheValue) {\n    return cacheValue;\n  }\n\n  const result = classNames\n    .split(/\\s+/)\n    .filter((className) => !className.includes(\"dark:\"))\n    .join(\" \");\n\n  cache.set(cacheKey, result);\n\n  return result;\n}\n"], "names": [], "mappings": ";;AACA,MAAM,KAAK,mBAAmB,IAAI,GAAG,EAAE;AAChC,SAAS,SAAS,CAAC,UAAU,EAAE;AACtC,EAAE,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,IAAI,EAAE;AACpD,IAAI,OAAO,UAAU;AACrB;AACA,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE;AACjC,IAAI,OAAO,UAAU;AACrB;AACA,EAAE,UAAU,GAAG,UAAU,CAAC,IAAI,EAAE;AAChC,EAAE,MAAM,QAAQ,GAAG,UAAU;AAC7B,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AACxC,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,OAAO,UAAU;AACrB;AACA,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACtG,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC7B,EAAE,OAAO,MAAM;AACf;;;;"}