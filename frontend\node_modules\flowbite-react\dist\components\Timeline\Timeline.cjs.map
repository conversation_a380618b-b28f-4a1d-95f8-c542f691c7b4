{"version": 3, "file": "Timeline.cjs", "sources": ["../../../src/components/Timeline/Timeline.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport { TimelineContext } from \"./TimelineContext\";\nimport type { TimelineItemTheme } from \"./TimelineItem\";\n\nexport interface TimelineTheme {\n  root: {\n    direction: {\n      horizontal: string;\n      vertical: string;\n    };\n  };\n  item: TimelineItemTheme;\n}\n\nexport interface TimelineProps extends ComponentProps<\"ol\">, ThemingProps<TimelineTheme> {\n  horizontal?: boolean;\n}\n\nexport const Timeline = forwardRef<HTMLOListElement, TimelineProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [timelineTheme, provider.theme?.timeline, props.theme],\n    [get(provider.clearTheme, \"timeline\"), props.clearTheme],\n    [get(provider.applyTheme, \"timeline\"), props.applyTheme],\n  );\n\n  const { className, horizontal, ...restProps } = resolveProps(props, provider.props?.timeline);\n\n  return (\n    <TimelineContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, horizontal }}\n    >\n      <ol\n        ref={ref}\n        data-testid=\"timeline-component\"\n        className={twMerge(\n          horizontal && theme.root.direction.horizontal,\n          !horizontal && theme.root.direction.vertical,\n          className,\n        )}\n        {...restProps}\n      />\n    </TimelineContext.Provider>\n  );\n});\n\nTimeline.displayName = \"Timeline\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "timelineTheme", "get", "resolveProps", "jsx", "TimelineContext", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC/F,EAAE,uBAAuBO,cAAG;AAC5B,IAAIC,+BAAe,CAAC,QAAQ;AAC5B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE;AAC3G,MAAM,QAAQ,kBAAkBD,cAAG;AACnC,QAAQ,IAAI;AACZ,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,aAAa,EAAE,oBAAoB;AAC7C,UAAU,SAAS,EAAEE,qBAAO;AAC5B,YAAY,UAAU,IAAIP,OAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU;AACzD,YAAY,CAAC,UAAU,IAAIA,OAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;AACxD,YAAY;AACZ,WAAW;AACX,UAAU,GAAG;AACb;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}