{"version": 3, "file": "Table.cjs", "sources": ["../../../src/components/Table/Table.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TableBodyTheme } from \"./TableBody\";\nimport { TableContext } from \"./TableContext\";\nimport type { TableHeadTheme } from \"./TableHead\";\nimport type { TableRowTheme } from \"./TableRow\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableTheme {\n  root: TableRootTheme;\n  head: TableHeadTheme;\n  row: TableRowTheme;\n  body: TableBodyTheme;\n}\n\nexport interface TableRootTheme {\n  base: string;\n  shadow: string;\n  wrapper: string;\n}\n\nexport interface TableProps extends ComponentPropsWithRef<\"table\">, ThemingProps<TableTheme> {\n  striped?: boolean;\n  hoverable?: boolean;\n}\n\nexport const Table = forwardRef<HTMLTableElement, TableProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme, provider.theme?.table, props.theme],\n    [get(provider.clearTheme, \"table\"), props.clearTheme],\n    [get(provider.applyTheme, \"table\"), props.applyTheme],\n  );\n\n  const { className, striped, hoverable, ...restProps } = resolveProps(props, provider.props?.table);\n\n  return (\n    <div data-testid=\"table-element\" className={twMerge(theme.root.wrapper)}>\n      <TableContext.Provider\n        value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, striped, hoverable }}\n      >\n        <div className={twMerge(theme.root.shadow, className)}></div>\n        <table ref={ref} className={twMerge(theme.root.base, className)} {...restProps} />\n      </TableContext.Provider>\n    </div>\n  );\n});\n\nTable.displayName = \"Table\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "tableTheme", "get", "resolveProps", "jsx", "twMerge", "jsxs", "TableContext"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,KAAK,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAChD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AACxD,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AACpG,EAAE,uBAAuBO,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,kBAAkBO,eAAI;AAC5I,IAAIC,yBAAY,CAAC,QAAQ;AACzB,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE;AACnH,MAAM,QAAQ,EAAE;AAChB,wBAAwBH,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;AACxF,wBAAwBK,cAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE;AAC1G;AACA;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}