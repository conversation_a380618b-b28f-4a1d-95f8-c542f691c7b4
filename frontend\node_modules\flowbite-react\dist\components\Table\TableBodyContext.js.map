{"version": 3, "file": "TableBodyContext.js", "sources": ["../../../src/components/Table/TableBodyContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TableBodyTheme } from \"./TableBody\";\n\nexport type TableBodyContextValue = ThemingProps<TableBodyTheme>;\n\nexport const TableBodyContext = createContext<TableBodyContextValue | undefined>(undefined);\n\nexport function useTableBodyContext(): TableBodyContextValue {\n  const context = useContext(TableBodyContext);\n\n  if (!context) {\n    throw new Error(\"useTableBodyContext should be used within the TableBodyContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,gBAAgB,GAAG,aAAa,CAAC,MAAM;AAC7C,SAAS,mBAAmB,GAAG;AACtC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC;AAC9C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC;AAC/F;AACA,EAAE,OAAO,OAAO;AAChB;;;;"}