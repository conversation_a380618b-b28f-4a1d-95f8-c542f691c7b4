{"version": 3, "file": "webpack.cjs", "sources": ["../../src/plugin/webpack.ts"], "sourcesContent": ["import type { WebpackPluginInstance } from \"webpack\";\nimport { build } from \"../cli/commands/build\";\nimport { dev } from \"../cli/commands/dev\";\nimport { pluginName } from \"./index\";\n\nexport default (): WebpackPluginInstance => ({\n  name: pluginN<PERSON>,\n  apply(compiler) {\n    let devServerStarted = false;\n\n    compiler.hooks.beforeCompile.tapPromise(pluginName, async () => {\n      const isDev = compiler.options.mode === \"development\";\n      const isBuild = compiler.options.mode === \"production\";\n\n      if (isBuild) {\n        await build();\n      } else if (isDev && !devServerStarted) {\n        devServerStarted = true;\n        await build();\n        await dev();\n      }\n    });\n  },\n});\n"], "names": ["pluginName", "build", "dev"], "mappings": ";;;;;;AAIA,cAAe,OAAO;AACtB,EAAE,IAAI,EAAEA,gBAAU;AAClB,EAAE,KAAK,CAAC,QAAQ,EAAE;AAClB,IAAI,IAAI,gBAAgB,GAAG,KAAK;AAChC,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAACA,gBAAU,EAAE,YAAY;AACpE,MAAM,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa;AAC3D,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY;AAC5D,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,MAAMC,WAAK,EAAE;AACrB,OAAO,MAAM,IAAI,KAAK,IAAI,CAAC,gBAAgB,EAAE;AAC7C,QAAQ,gBAAgB,GAAG,IAAI;AAC/B,QAAQ,MAAMA,WAAK,EAAE;AACrB,QAAQ,MAAMC,OAAG,EAAE;AACnB;AACA,KAAK,CAAC;AACN;AACA,CAAC,CAAC;;;;"}