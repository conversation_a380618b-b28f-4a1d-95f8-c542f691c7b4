{"version": 3, "file": "ToggleSwitch.cjs", "sources": ["../../../src/components/ToggleSwitch/ToggleSwitch.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, KeyboardEvent } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteBoolean, FlowbiteColors, ThemingProps } from \"../../types\";\nimport type { TextInputSizes } from \"../TextInput\";\nimport { toggleSwitchTheme } from \"./theme\";\n\nexport interface ToggleSwitchTheme {\n  root: ToggleSwitchRootTheme;\n  toggle: ToggleSwitchToggleTheme;\n}\n\nexport interface ToggleSwitchRootTheme {\n  base: string;\n  active: FlowbiteBoolean;\n  label: string;\n  input: string;\n}\n\nexport interface ToggleSwitchToggleTheme {\n  base: string;\n  sizes: TextInputSizes;\n  checked: FlowbiteBoolean & {\n    color: FlowbiteColors;\n  };\n}\n\nexport type ToggleSwitchProps = Omit<ComponentProps<\"button\">, \"onChange\" | \"ref\"> & {\n  checked: boolean;\n  color?: DynamicStringEnumKeysOf<FlowbiteColors>;\n  sizing?: DynamicStringEnumKeysOf<TextInputSizes>;\n  label?: string;\n  onChange: (checked: boolean) => void;\n} & ThemingProps<ToggleSwitchTheme>;\n\nexport const ToggleSwitch = forwardRef<HTMLInputElement, ToggleSwitchProps>((props, ref) => {\n  const id = useId();\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [toggleSwitchTheme, provider.theme?.toggleSwitch, props.theme],\n    [get(provider.clearTheme, \"toggleSwitch\"), props.clearTheme],\n    [get(provider.applyTheme, \"toggleSwitch\"), props.applyTheme],\n  );\n\n  const {\n    checked,\n    className,\n    color = \"default\",\n    sizing = \"md\",\n    disabled,\n    label,\n    name,\n    onChange,\n    ...restProps\n  } = resolveProps(props, provider.props?.toggleSwitch);\n\n  function handleClick() {\n    onChange(!checked);\n  }\n\n  function handleOnKeyDown(event: KeyboardEvent<HTMLButtonElement>) {\n    if (event.code == \"Enter\") {\n      event.preventDefault();\n    }\n  }\n\n  return (\n    <>\n      <input ref={ref} checked={checked} name={name} type=\"checkbox\" className={theme.root.input} readOnly hidden />\n      <button\n        aria-checked={checked}\n        aria-labelledby={`${id}-flowbite-toggleswitch-label`}\n        disabled={disabled}\n        id={`${id}-flowbite-toggleswitch`}\n        onClick={handleClick}\n        onKeyDown={handleOnKeyDown}\n        role=\"switch\"\n        tabIndex={0}\n        type=\"button\"\n        className={twMerge(theme.root.base, theme.root.active[disabled ? \"off\" : \"on\"], className)}\n        {...restProps}\n      >\n        <div\n          data-testid=\"flowbite-toggleswitch-toggle\"\n          className={twMerge(\n            theme.toggle.base,\n            theme.toggle.checked.color[color],\n            theme.toggle.checked[checked ? \"on\" : \"off\"],\n            theme.toggle.sizes[sizing],\n          )}\n        />\n        {!!label?.length && (\n          <span\n            data-testid=\"flowbite-toggleswitch-label\"\n            id={`${id}-flowbite-toggleswitch-label`}\n            className={theme.root.label}\n          >\n            {label}\n          </span>\n        )}\n      </button>\n    </>\n  );\n});\n\nToggleSwitch.displayName = \"ToggleSwitch\";\n"], "names": ["forwardRef", "useId", "provider", "useThemeProvider", "theme", "useResolveTheme", "toggleSwitchTheme", "get", "resolveProps", "jsxs", "Fragment", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,YAAY,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,GAAGC,WAAK,EAAE;AACpB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,uBAAiB,EAAEJ,UAAQ,CAAC,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC;AAClE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAChE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,SAAS;AACrB,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvD,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAC;AACtB;AACA,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,EAAE;AAC/B,MAAM,KAAK,CAAC,cAAc,EAAE;AAC5B;AACA;AACA,EAAE,uBAAuBO,eAAI,CAACC,mBAAQ,EAAE,EAAE,QAAQ,EAAE;AACpD,oBAAoBC,cAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAEP,OAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACrI,oBAAoBK,eAAI;AACxB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,cAAc,EAAE,OAAO;AAC/B,QAAQ,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC,4BAA4B,CAAC;AAC9D,QAAQ,QAAQ;AAChB,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,sBAAsB,CAAC;AACzC,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,SAAS,EAAE,eAAe;AAClC,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAEG,qBAAO,CAACR,OAAK,CAAC,IAAI,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC;AAClG,QAAQ,GAAG,SAAS;AACpB,QAAQ,QAAQ,EAAE;AAClB,0BAA0BO,cAAG;AAC7B,YAAY,KAAK;AACjB,YAAY;AACZ,cAAc,aAAa,EAAE,8BAA8B;AAC3D,cAAc,SAAS,EAAEC,qBAAO;AAChC,gBAAgBR,OAAK,CAAC,MAAM,CAAC,IAAI;AACjC,gBAAgBA,OAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACjD,gBAAgBA,OAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AAC5D,gBAAgBA,OAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;AACzC;AACA;AACA,WAAW;AACX,UAAU,CAAC,CAAC,KAAK,EAAE,MAAM,oBAAoBO,cAAG;AAChD,YAAY,MAAM;AAClB,YAAY;AACZ,cAAc,aAAa,EAAE,6BAA6B;AAC1D,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,4BAA4B,CAAC;AACrD,cAAc,SAAS,EAAEP,OAAK,CAAC,IAAI,CAAC,KAAK;AACzC,cAAc,QAAQ,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}