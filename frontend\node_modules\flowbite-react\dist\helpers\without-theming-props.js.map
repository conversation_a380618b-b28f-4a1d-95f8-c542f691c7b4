{"version": 3, "file": "without-theming-props.js", "sources": ["../../src/helpers/without-theming-props.ts"], "sourcesContent": ["import type { WithoutThemingProps } from \"../types\";\n\n/**\n * Removes theming properties (theme, clearTheme, applyTheme)\n *\n * @template T - The type of the object\n * @param {T} props - The object to remove theming properties from\n * @returns {WithoutThemingProps<T>} The object without theming properties (theme, clearTheme, applyTheme)\n */\nexport function withoutThemingProps<T>(props: T): WithoutThemingProps<T> {\n  // @ts-expect-error - bypass\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const { theme, clearTheme, applyTheme, ...rest } = props;\n\n  return rest;\n}\n"], "names": [], "mappings": "AACO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK;AAC1D,EAAE,OAAO,IAAI;AACb;;;;"}