{"version": 3, "file": "TimelineContentContext.cjs", "sources": ["../../../src/components/Timeline/TimelineContentContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TimelineContentTheme } from \"./TimelineContent\";\n\nexport type TimelineContentContextValue = ThemingProps<TimelineContentTheme>;\n\nexport const TimelineContentContext = createContext<TimelineContentContextValue | undefined>(undefined);\n\nexport function useTimelineContentContext(): TimelineContentContextValue {\n  const context = useContext(TimelineContentContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineContentContext should be used within the TimelineContentContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,sBAAsB,GAAGA,mBAAa,CAAC,MAAM;AACnD,SAAS,yBAAyB,GAAG;AAC5C,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,sBAAsB,CAAC;AACpD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC;AAC3G;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}