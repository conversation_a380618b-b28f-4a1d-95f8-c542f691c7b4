{"version": 3, "file": "TableContext.cjs", "sources": ["../../../src/components/Table/TableContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TableTheme } from \"./Table\";\n\nexport interface TableContextValue extends ThemingProps<TableTheme> {\n  striped?: boolean;\n  hoverable?: boolean;\n}\n\nexport const TableContext = createContext<TableContextValue | undefined>(undefined);\n\nexport function useTableContext(): TableContextValue {\n  const context = useContext(TableContext);\n\n  if (!context) {\n    throw new Error(\"useTableContext should be used within the TableContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,YAAY,GAAGA,mBAAa,CAAC,MAAM;AACzC,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,YAAY,CAAC;AAC1C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}