{"version": 3, "file": "ToastContext.js", "sources": ["../../../src/components/Toast/ToastContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { ToastDuration, ToastTheme } from \"./Toast\";\n\nexport interface ToastContextValue extends ThemingProps<ToastTheme> {\n  duration?: ToastDuration;\n  isClosed?: boolean;\n  isRemoved?: boolean;\n  setIsClosed: (isClosed: boolean) => void;\n  setIsRemoved: (isRemoved: boolean) => void;\n}\n\nexport const ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToastContext(): ToastContextValue {\n  const context = useContext(ToastContext);\n\n  if (!context) {\n    throw new Error(\"useToastContext should be used within the ToastContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,YAAY,GAAG,aAAa,CAAC,MAAM;AACzC,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC;AAC1C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA,EAAE,OAAO,OAAO;AAChB;;;;"}