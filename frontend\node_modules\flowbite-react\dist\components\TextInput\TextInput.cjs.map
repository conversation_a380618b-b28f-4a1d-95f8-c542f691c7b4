{"version": 3, "file": "TextInput.cjs", "sources": ["../../../src/components/TextInput/TextInput.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, ReactNode } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type {\n  DynamicStringEnumKeysOf,\n  FlowbiteBoolean,\n  FlowbiteColors,\n  FlowbiteSizes,\n  ThemingProps,\n} from \"../../types\";\nimport { textInputTheme } from \"./theme\";\n\nexport interface TextInputTheme {\n  base: string;\n  addon: string;\n  field: {\n    base: string;\n    icon: {\n      base: string;\n      svg: string;\n    };\n    rightIcon: {\n      base: string;\n      svg: string;\n    };\n    input: {\n      base: string;\n      sizes: TextInputSizes;\n      colors: TextInputColors;\n      withIcon: FlowbiteBoolean;\n      withRightIcon: FlowbiteBoolean;\n      withAddon: FlowbiteBoolean;\n      withShadow: FlowbiteBoolean;\n    };\n  };\n}\n\nexport interface TextInputColors extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface TextInputSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface TextInputProps extends Omit<ComponentProps<\"input\">, \"ref\" | \"color\">, ThemingProps<TextInputTheme> {\n  addon?: ReactNode;\n  color?: DynamicStringEnumKeysOf<TextInputColors>;\n  icon?: FC<ComponentProps<\"svg\">>;\n  rightIcon?: FC<ComponentProps<\"svg\">>;\n  shadow?: boolean;\n  sizing?: DynamicStringEnumKeysOf<TextInputSizes>;\n}\n\nexport const TextInput = forwardRef<HTMLInputElement, TextInputProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [textInputTheme, provider.theme?.textInput, props.theme],\n    [get(provider.clearTheme, \"textInput\"), props.clearTheme],\n    [get(provider.applyTheme, \"textInput\"), props.applyTheme],\n  );\n\n  const {\n    addon,\n    className,\n    color = \"gray\",\n    icon: Icon,\n    rightIcon: RightIcon,\n    shadow,\n    sizing = \"md\",\n    type = \"text\",\n    ...restProps\n  } = resolveProps(props, provider.props?.textInput);\n\n  return (\n    <div className={twMerge(theme.base, className)}>\n      {addon && <span className={theme.addon}>{addon}</span>}\n      <div className={theme.field.base}>\n        {Icon && (\n          <div className={theme.field.icon.base}>\n            <Icon className={theme.field.icon.svg} />\n          </div>\n        )}\n        {RightIcon && (\n          <div data-testid=\"right-icon\" className={theme.field.rightIcon.base}>\n            <RightIcon className={theme.field.rightIcon.svg} />\n          </div>\n        )}\n        <input\n          className={twMerge(\n            theme.field.input.base,\n            theme.field.input.colors[color],\n            theme.field.input.sizes[sizing],\n            theme.field.input.withIcon[Icon ? \"on\" : \"off\"],\n            theme.field.input.withRightIcon[RightIcon ? \"on\" : \"off\"],\n            theme.field.input.withAddon[addon ? \"on\" : \"off\"],\n            theme.field.input.withShadow[shadow ? \"on\" : \"off\"],\n          )}\n          type={type}\n          {...restProps}\n          ref={ref}\n        />\n      </div>\n    </div>\n  );\n});\n\nTextInput.displayName = \"TextInput\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "textInputTheme", "get", "resolveProps", "jsxs", "twMerge", "jsx"], "mappings": ";;;;;;;;;;;AAUY,MAAC,SAAS,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,oBAAc,EAAEJ,UAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC;AAC5D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,MAAM;AACV,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,IAAI,GAAG,MAAM;AACjB,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpD,EAAE,uBAAuBO,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE;AAC5F,IAAI,KAAK,oBAAoBO,cAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAEP,OAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AACrF,oBAAoBK,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEL,OAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AACzE,MAAM,IAAI,oBAAoBO,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEP,OAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,kBAAkBO,cAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAEP,OAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AACxJ,MAAM,SAAS,oBAAoBO,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAEP,OAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,kBAAkBO,cAAG,CAAC,SAAS,EAAE,EAAE,SAAS,EAAEP,OAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AACzM,sBAAsBO,cAAG;AACzB,QAAQ,OAAO;AACf,QAAQ;AACR,UAAU,SAAS,EAAED,qBAAO;AAC5B,YAAYN,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;AAClC,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3C,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC3C,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAC3D,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;AACrE,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;AAC7D,YAAYA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK;AAC9D,WAAW;AACX,UAAU,IAAI;AACd,UAAU,GAAG,SAAS;AACtB,UAAU;AACV;AACA;AACA,KAAK,EAAE;AACP,GAAG,EAAE,CAAC;AACN,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}