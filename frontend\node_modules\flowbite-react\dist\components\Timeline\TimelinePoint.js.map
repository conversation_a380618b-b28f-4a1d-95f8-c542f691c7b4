{"version": 3, "file": "TimelinePoint.js", "sources": ["../../../src/components/Timeline/TimelinePoint.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\n\nexport interface TimelinePointTheme {\n  horizontal: string;\n  line: string;\n  marker: {\n    base: {\n      horizontal: string;\n      vertical: string;\n    };\n    icon: {\n      base: string;\n      wrapper: string;\n    };\n  };\n  vertical: string;\n}\n\nexport interface TimelinePointProps extends ComponentProps<\"div\">, ThemingProps<TimelinePointTheme> {\n  icon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const TimelinePoint = forwardRef<HTMLDivElement, TimelinePointProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, horizontal } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.point,\n      provider.theme?.timeline?.item?.point,\n      rootTheme?.item?.point,\n      itemTheme?.point,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.point\"),\n      get(rootClearTheme, \"item.point\"),\n      get(itemClearTheme, \"point\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.point\"),\n      get(rootApplyTheme, \"item.point\"),\n      get(itemApplyTheme, \"point\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { children, className, icon: Icon, ...restProps } = resolveProps(props, provider.props?.timelinePoint);\n\n  return (\n    <div\n      ref={ref}\n      data-testid=\"timeline-point\"\n      className={twMerge(horizontal && theme.horizontal, !horizontal && theme.vertical, className)}\n      {...restProps}\n    >\n      {children}\n      {Icon ? (\n        <span className={twMerge(theme.marker.icon.wrapper)}>\n          <Icon aria-hidden className={twMerge(theme.marker.icon.base)} />\n        </span>\n      ) : (\n        <div\n          className={twMerge(horizontal && theme.marker.base.horizontal, !horizontal && theme.marker.base.vertical)}\n        />\n      )}\n      {horizontal && <div className={twMerge(theme.line)} />}\n    </div>\n  );\n});\n\nTimelinePoint.displayName = \"TimelinePoint\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,aAAa,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACxD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,kBAAkB,EAAE;AACvH,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,sBAAsB,EAAE;AAC/G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI;AACJ,MAAM,aAAa,CAAC,IAAI,CAAC,KAAK;AAC9B,MAAM,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK;AAC3C,MAAM,SAAS,EAAE,IAAI,EAAE,KAAK;AAC5B,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,qBAAqB,CAAC;AACrD,MAAM,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC;AACvC,MAAM,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC;AAClC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,qBAAqB,CAAC;AACrD,MAAM,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC;AACvC,MAAM,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC;AAClC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;AAC9G,EAAE,uBAAuB,IAAI;AAC7B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,SAAS,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;AAClG,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,QAAQ,QAAQ;AAChB,QAAQ,IAAI,mBAAmB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,GAAG;AAC7N,UAAU,KAAK;AACf,UAAU;AACV,YAAY,SAAS,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACpH;AACA,SAAS;AACT,QAAQ,UAAU,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACnF;AACA;AACA,GAAG;AACH,CAAC;AACD,aAAa,CAAC,WAAW,GAAG,eAAe;;;;"}