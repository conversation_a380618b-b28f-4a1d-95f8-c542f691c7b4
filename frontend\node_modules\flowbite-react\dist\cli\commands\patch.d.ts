/**
 * Patches Tailwind CSS installation to ensure version files exist and are correctly configured.
 *
 * This function:
 * - Resolves the Tailwind CSS module path
 * - Reads the Tailwind package.json to get the actual version
 * - Creates or updates version files (version.js, version.mjs, version.d.ts, version.d.mts)
 * - Updates package.json exports if necessary
 *
 * @returns {Promise<void>} A promise that resolves when patching is complete
 */
export declare function patchTailwind(): Promise<void>;
