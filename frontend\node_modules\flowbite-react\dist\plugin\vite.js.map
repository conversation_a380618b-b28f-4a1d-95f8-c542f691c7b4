{"version": 3, "file": "vite.js", "sources": ["../../src/plugin/vite.ts"], "sourcesContent": ["import type { Plugin } from \"vite\";\nimport { build } from \"../cli/commands/build\";\nimport { dev } from \"../cli/commands/dev\";\nimport { pluginName } from \"./index\";\n\nexport default (): Plugin => ({\n  name: pluginName,\n  async buildStart() {\n    await build();\n  },\n  async configureServer() {\n    await dev();\n  },\n});\n"], "names": [], "mappings": ";;;;AAIA,iBAAe,OAAO;AACtB,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,MAAM,KAAK,EAAE;AACjB,GAAG;AACH,EAAE,MAAM,eAAe,GAAG;AAC1B,IAAI,MAAM,GAAG,EAAE;AACf;AACA,CAAC,CAAC;;;;"}