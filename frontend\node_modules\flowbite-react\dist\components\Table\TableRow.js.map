{"version": 3, "file": "TableRow.js", "sources": ["../../../src/components/Table/TableRow.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableRowTheme {\n  base: string;\n  hovered: string;\n  striped: string;\n}\n\nexport interface TableRowProps extends ComponentPropsWithRef<\"tr\">, ThemingProps<TableRowTheme> {}\n\nexport const TableRow = forwardRef<HTMLTableRowElement, TableRowProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    hoverable,\n    striped,\n  } = useTableContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.row, provider.theme?.table?.row, rootTheme?.row, props.theme],\n    [get(provider.clearTheme, \"table.row\"), get(rootClearTheme, \"row\"), props.clearTheme],\n    [get(provider.applyTheme, \"table.row\"), get(rootApplyTheme, \"row\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableRow);\n\n  return (\n    <tr\n      ref={ref}\n      data-testid=\"table-row-element\"\n      className={twMerge(theme.base, striped && theme.striped, hoverable && theme.hovered, className)}\n      {...restProps}\n    />\n  );\n});\n\nTableRow.displayName = \"TableRow\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,QAAQ,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAG,eAAe,EAAE;AACvB,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC;AAC7E,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU;AACxF,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnF,EAAE,uBAAuB,GAAG;AAC5B,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC;AACrG,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}