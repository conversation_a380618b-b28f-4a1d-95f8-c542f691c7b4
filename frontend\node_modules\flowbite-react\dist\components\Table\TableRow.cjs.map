{"version": 3, "file": "TableRow.cjs", "sources": ["../../../src/components/Table/TableRow.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableRowTheme {\n  base: string;\n  hovered: string;\n  striped: string;\n}\n\nexport interface TableRowProps extends ComponentPropsWithRef<\"tr\">, ThemingProps<TableRowTheme> {}\n\nexport const TableRow = forwardRef<HTMLTableRowElement, TableRowProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    hoverable,\n    striped,\n  } = useTableContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.row, provider.theme?.table?.row, rootTheme?.row, props.theme],\n    [get(provider.clearTheme, \"table.row\"), get(rootClearTheme, \"row\"), props.clearTheme],\n    [get(provider.applyTheme, \"table.row\"), get(rootApplyTheme, \"row\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableRow);\n\n  return (\n    <tr\n      ref={ref}\n      data-testid=\"table-row-element\"\n      className={twMerge(theme.base, striped && theme.striped, hoverable && theme.hovered, className)}\n      {...restProps}\n    />\n  );\n});\n\nTableRow.displayName = \"TableRow\";\n"], "names": ["forwardRef", "useTableContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "tableTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAGC,4BAAe,EAAE;AACvB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,GAAG,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC;AAC7E,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzF,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU;AACxF,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnF,EAAE,uBAAuBO,cAAG;AAC5B,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,OAAO,IAAIA,OAAK,CAAC,OAAO,EAAE,SAAS,IAAIA,OAAK,CAAC,OAAO,EAAE,SAAS,CAAC;AACrG,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}