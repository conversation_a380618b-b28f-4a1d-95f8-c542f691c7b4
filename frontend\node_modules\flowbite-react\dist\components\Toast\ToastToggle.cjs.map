{"version": 3, "file": "ToastToggle.cjs", "sources": ["../../../src/components/Toast/ToastToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC, type MouseEvent } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { XIcon as DefaultXIcon } from \"../../icons/x-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { toastTheme } from \"./theme\";\nimport { useToastContext } from \"./ToastContext\";\n\nexport interface ToastToggleTheme {\n  base: string;\n  icon: string;\n}\n\nexport interface ToastToggleProps extends ComponentProps<\"button\">, ThemingProps<ToastToggleTheme> {\n  xIcon?: FC<ComponentProps<\"svg\">>;\n  onDismiss?: () => void;\n}\n\nexport const ToastToggle = forwardRef<HTMLButtonElement, ToastToggleProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    duration,\n    isClosed,\n    isRemoved,\n    setIsClosed,\n    setIsRemoved,\n  } = useToastContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [toastTheme.toggle, provider.theme?.toast?.toggle, rootTheme?.toggle, props.theme],\n    [get(provider.clearTheme, \"toast.toggle\"), get(rootClearTheme, \"toggle\"), props.clearTheme],\n    [get(provider.applyTheme, \"toast.toggle\"), get(rootApplyTheme, \"toggle\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    onClick,\n    onDismiss,\n    xIcon: XIcon = DefaultXIcon,\n    ...restProps\n  } = resolveProps(props, provider.props?.toastToggle);\n\n  function handleClick(e: MouseEvent<HTMLButtonElement>) {\n    if (onClick) {\n      onClick(e);\n    }\n\n    if (onDismiss) {\n      onDismiss();\n      return;\n    }\n\n    setIsClosed(!isClosed);\n    setTimeout(() => setIsRemoved(!isRemoved), duration);\n  }\n\n  return (\n    <button\n      ref={ref}\n      aria-label=\"Close\"\n      onClick={handleClick}\n      type=\"button\"\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    >\n      <XIcon aria-hidden className={theme.icon} />\n    </button>\n  );\n});\nToastToggle.displayName = \"ToastToggle\";\n"], "names": ["forwardRef", "useToastContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "toastTheme", "get", "DefaultXIcon", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,WAAW,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI;AACJ,GAAG,GAAGC,4BAAe,EAAE;AACvB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,MAAM,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,KAAK,EAAE,KAAK,GAAGC,WAAY;AAC/B,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEP,UAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtD,EAAE,SAAS,WAAW,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,EAAE;AACjB,MAAM;AACN;AACA,IAAI,WAAW,CAAC,CAAC,QAAQ,CAAC;AAC1B,IAAI,UAAU,CAAC,MAAM,YAAY,CAAC,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC;AACxD;AACA,EAAE,uBAAuBQ,cAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,YAAY,EAAE,OAAO;AAC3B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,SAAS,EAAEC,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,kBAAkBM,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAEN,OAAK,CAAC,IAAI,EAAE;AACzF;AACA,GAAG;AACH,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}