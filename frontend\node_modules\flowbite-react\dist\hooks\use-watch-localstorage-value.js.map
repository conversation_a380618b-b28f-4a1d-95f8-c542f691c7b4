{"version": 3, "file": "use-watch-localstorage-value.js", "sources": ["../../src/hooks/use-watch-localstorage-value.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\n/**\n * Triggers `onChange` when another browser tab instance mutates the LS value.\n */\nexport function useWatchLocalStorageValue({\n  key: watchKey,\n  onChange,\n}: {\n  key: string;\n  onChange(newValue: string | null): void;\n}) {\n  function handleStorageChange({ key, newValue }: StorageEvent) {\n    if (key === watchKey) onChange(newValue);\n  }\n\n  useEffect(() => {\n    window.addEventListener(\"storage\", handleStorageChange);\n    return () => window.removeEventListener(\"storage\", handleStorageChange);\n  }, []);\n}\n"], "names": [], "mappings": ";;AAGO,SAAS,yBAAyB,CAAC;AAC1C,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE;AACF,CAAC,EAAE;AACH,EAAE,SAAS,mBAAmB,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE;AAClD,IAAI,IAAI,GAAG,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC;AAC5C;AACA,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,mBAAmB,CAAC;AAC3D,IAAI,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC;AAC3E,GAAG,EAAE,EAAE,CAAC;AACR;;;;"}