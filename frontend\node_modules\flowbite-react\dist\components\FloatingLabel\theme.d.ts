export declare const floatingLabelTheme: {
    input: {
        default: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
        success: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
        error: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
    };
    label: {
        default: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
        success: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
        error: {
            filled: {
                sm: string;
                md: string;
            };
            outlined: {
                sm: string;
                md: string;
            };
            standard: {
                sm: string;
                md: string;
            };
        };
    };
};
export type FloatingLabelTheme = typeof floatingLabelTheme;
