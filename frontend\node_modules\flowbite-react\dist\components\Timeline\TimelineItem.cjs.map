{"version": 3, "file": "TimelineItem.cjs", "sources": ["../../../src/components/Timeline/TimelineItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport type { TimelineContentTheme } from \"./TimelineContent\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { TimelineItemContext } from \"./TimelineItemContext\";\nimport type { TimelinePointTheme } from \"./TimelinePoint\";\n\nexport interface TimelineItemTheme {\n  root: {\n    horizontal: string;\n    vertical: string;\n  };\n  content: TimelineContentTheme;\n  point: TimelinePointTheme;\n}\n\nexport interface TimelineItemProps extends ComponentProps<\"li\">, ThemingProps<TimelineItemTheme> {}\n\nexport const TimelineItem = forwardRef<HTMLLIElement, TimelineItemProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, horizontal } = useTimelineContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [timelineTheme.item, provider.theme?.timeline?.item, rootTheme?.item, props.theme],\n    [get(provider.clearTheme, \"timeline.item\"), get(rootClearTheme, \"item\"), props.clearTheme],\n    [get(provider.applyTheme, \"timeline.item\"), get(rootApplyTheme, \"item\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.timelineItem);\n\n  return (\n    <TimelineItemContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme }}\n    >\n      <li\n        ref={ref}\n        data-testid=\"timeline-item\"\n        className={twMerge(horizontal && theme.root.horizontal, !horizontal && theme.root.vertical, className)}\n        {...restProps}\n      />\n    </TimelineItemContext.Provider>\n  );\n});\n\nTimelineItem.displayName = \"TimelineItem\";\n"], "names": ["forwardRef", "useTimelineContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "timelineTheme", "get", "resolveProps", "jsx", "TimelineItemContext", "twMerge"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,YAAY,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GAAGC,kCAAkB,EAAE;AACvH,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACtF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC9F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC7F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvF,EAAE,uBAAuBO,cAAG;AAC5B,IAAIC,uCAAmB,CAAC,QAAQ;AAChC,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;AAC/F,MAAM,QAAQ,kBAAkBD,cAAG;AACnC,QAAQ,IAAI;AACZ,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,aAAa,EAAE,eAAe;AACxC,UAAU,SAAS,EAAEE,qBAAO,CAAC,UAAU,IAAIP,OAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,IAAIA,OAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAChH,UAAU,GAAG;AACb;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}