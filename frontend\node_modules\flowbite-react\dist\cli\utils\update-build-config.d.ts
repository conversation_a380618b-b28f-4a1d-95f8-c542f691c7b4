/**
 * Updates the build configuration by adding a plugin to the build options.
 *
 * @param {Object} options - The options for updating the build config.
 * @param {string} options.content - The source code content to modify.
 * @param {string} [options.pluginName='flowbiteReact'] - The name to use for the imported plugin.
 * @param {string} options.pluginImportPath - The import path for the plugin.
 * @returns {string} The modified source code with the plugin added.
 */
export declare function updateBuildConfig({ content, pluginName, pluginImportPath, }: {
    content: string;
    pluginName: string;
    pluginImportPath: string;
}): string;
