export interface Config {
    $schema: string;
    components: string[];
    dark: boolean;
    path: string;
    prefix: string;
    rsc: boolean;
    tsx: boolean;
}
/**
 * Reads the configuration file and returns its content as a Config object.
 *
 * This function attempts to read the file specified by `configFilePath`, parse its content as JSON,
 * and return the parsed configuration. If the file cannot be read or parsed, a default Config object is returned.
 *
 * @returns {Promise<Config>} A promise that resolves to a Config object representing the configuration.
 */
export declare function getConfig(): Promise<Config>;
