{"version": 3, "file": "TimelineTitle.cjs", "sources": ["../../../src/components/Timeline/TimelineTitle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteHeadingLevel, ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\n\nexport interface TimelineTitleTheme {\n  base: string;\n}\n\nexport interface TimelineTitleProps extends ComponentProps<\"h1\">, ThemingProps<TimelineTitleTheme> {\n  as?: FlowbiteHeadingLevel;\n}\n\nexport const TimelineTitle = forwardRef<HTMLHeadingElement, TimelineTitleProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n  const {\n    theme: contentTheme,\n    clearTheme: contentClearTheme,\n    applyTheme: contentApplyTheme,\n  } = useTimelineContentContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.content.title,\n      provider.theme?.timeline?.item?.content?.title,\n      rootTheme?.item?.content?.title,\n      itemTheme?.content?.title,\n      contentTheme?.title,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.content.title\"),\n      get(rootClearTheme, \"item.content.title\"),\n      get(itemClearTheme, \"content.title\"),\n      get(contentClearTheme, \"title\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.content.title\"),\n      get(rootApplyTheme, \"item.content.title\"),\n      get(itemApplyTheme, \"content.title\"),\n      get(contentApplyTheme, \"title\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { as: Component = \"h3\", className, ...restProps } = resolveProps(props, provider.props?.timelineTitle);\n\n  return <Component ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTimelineTitle.displayName = \"TimelineTitle\";\n"], "names": ["forwardRef", "useTimelineContext", "useTimelineItemContext", "useTimelineContentContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "timelineTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;;;AAaY,MAAC,aAAa,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACxD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,kCAAkB,EAAE;AAC3G,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,0CAAsB,EAAE;AAC/G,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,YAAY;AACvB,IAAI,UAAU,EAAE,iBAAiB;AACjC,IAAI,UAAU,EAAE;AAChB,GAAG,GAAGC,gDAAyB,EAAE;AACjC,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI;AACJ,MAAMC,mBAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;AACtC,MAAMJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;AACpD,MAAM,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;AACrC,MAAM,SAAS,EAAE,OAAO,EAAE,KAAK;AAC/B,MAAM,YAAY,EAAE,KAAK;AACzB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,6BAA6B,CAAC;AAC7D,MAAMK,OAAG,CAAC,cAAc,EAAE,oBAAoB,CAAC;AAC/C,MAAMA,OAAG,CAAC,cAAc,EAAE,eAAe,CAAC;AAC1C,MAAMA,OAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC;AACrC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,6BAA6B,CAAC;AAC7D,MAAMK,OAAG,CAAC,cAAc,EAAE,oBAAoB,CAAC;AAC/C,MAAMA,OAAG,CAAC,cAAc,EAAE,eAAe,CAAC;AAC1C,MAAMA,OAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC;AACrC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,GAAG,IAAI,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;AAC9G,EAAE,uBAAuBO,cAAG,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACzG,CAAC;AACD,aAAa,CAAC,WAAW,GAAG,eAAe;;;;"}