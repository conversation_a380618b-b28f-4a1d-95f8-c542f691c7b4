{"version": 3, "file": "TabItem.js", "sources": ["../../../src/components/Tabs/TabItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC, type ReactNode } from \"react\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useThemeProvider } from \"../../theme\";\n\nexport interface TabItemProps extends Omit<ComponentProps<\"div\">, \"title\"> {\n  active?: boolean;\n  disabled?: boolean;\n  icon?: FC<ComponentProps<\"svg\">>;\n  title: ReactNode;\n}\n\nexport const TabItem = forwardRef<HTMLDivElement, TabItemProps>((props, ref) => {\n  const provider = useThemeProvider();\n\n  const { title: _, ...restProps } = resolveProps(props, provider.props?.tabItem);\n\n  return <div ref={ref} {...restProps} />;\n});\n\nTabItem.displayName = \"TabItem\";\n"], "names": [], "mappings": ";;;;;;;;AAMY,MAAC,OAAO,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAClD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AACjF,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;AAC1D,CAAC;AACD,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}