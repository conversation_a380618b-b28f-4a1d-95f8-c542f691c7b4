{"version": 3, "file": "sun-icon.cjs", "sources": ["../../src/icons/sun-icon.tsx"], "sourcesContent": ["import { forwardRef, type SVGProps } from \"react\";\n\nexport const SunIcon = forwardRef<SVGSVGElement, SVGProps<SVGSVGElement>>((props, ref) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"1em\"\n    height=\"1em\"\n    fill=\"currentColor\"\n    stroke=\"currentColor\"\n    strokeWidth={0}\n    viewBox=\"0 0 20 20\"\n    ref={ref}\n    {...props}\n  >\n    <path\n      fillRule=\"evenodd\"\n      stroke=\"none\"\n      d=\"M10 2a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0V3a1 1 0 0 1 1-1zm4 8a4 4 0 1 1-8 0 4 4 0 0 1 8 0zm-.464 4.95.707.707a1 1 0 0 0 1.414-1.414l-.707-.707a1 1 0 0 0-1.414 1.414zm2.12-10.607a1 1 0 0 1 0 1.414l-.706.707a1 1 0 1 1-1.414-1.414l.707-.707a1 1 0 0 1 1.414 0zM17 11a1 1 0 1 0 0-2h-1a1 1 0 1 0 0 2h1zm-7 4a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0v-1a1 1 0 0 1 1-1zM5.05 6.464A1 1 0 1 0 6.465 5.05l-.708-.707a1 1 0 0 0-1.414 1.414l.707.707zm1.414 8.486-.707.707a1 1 0 0 1-1.414-1.414l.707-.707a1 1 0 0 1 1.414 1.414zM4 11a1 1 0 1 0 0-2H3a1 1 0 0 0 0 2h1z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n));\nSunIcon.displayName = \"SunIcon\";\n"], "names": ["forwardRef", "jsx"], "mappings": ";;;;;AAGY,MAAC,OAAO,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,qBAAqBC,cAAG;AACrE,EAAE,KAAK;AACP,EAAE;AACF,IAAI,KAAK,EAAE,4BAA4B;AACvC,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,GAAG;AACP,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,kBAAkBA,cAAG;AACjC,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,CAAC,EAAE,uhBAAuhB;AACliB,QAAQ,QAAQ,EAAE;AAClB;AACA;AACA;AACA,CAAC;AACD,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}