{"version": 3, "file": "TimelineBody.js", "sources": ["../../../src/components/Timeline/TimelineBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\n\nexport interface TimelineBodyTheme {\n  base: string;\n}\n\nexport interface TimelineBodyProps extends ComponentProps<\"p\">, ThemingProps<TimelineBodyTheme> {}\n\nexport const TimelineBody = forwardRef<HTMLDivElement, TimelineBodyProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n  const {\n    theme: contentTheme,\n    clearTheme: contentClearTheme,\n    applyTheme: contentApplyTheme,\n  } = useTimelineContentContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.content.body,\n      provider.theme?.timeline?.item?.content?.body,\n      rootTheme?.item?.content?.body,\n      itemTheme?.content?.body,\n      contentTheme?.body,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.content.body\"),\n      get(rootClearTheme, \"item.content.body\"),\n      get(itemClearTheme, \"content.body\"),\n      get(contentClearTheme, \"body\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.content.body\"),\n      get(rootApplyTheme, \"item.content.body\"),\n      get(itemApplyTheme, \"content.body\"),\n      get(contentApplyTheme, \"body\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.timelineBody);\n\n  return <div ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTimelineBody.displayName = \"TimelineBody\";\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaY,MAAC,YAAY,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,kBAAkB,EAAE;AAC3G,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,sBAAsB,EAAE;AAC/G,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,YAAY;AACvB,IAAI,UAAU,EAAE,iBAAiB;AACjC,IAAI,UAAU,EAAE;AAChB,GAAG,GAAG,yBAAyB,EAAE;AACjC,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI;AACJ,MAAM,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;AACrC,MAAM,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACnD,MAAM,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACpC,MAAM,SAAS,EAAE,OAAO,EAAE,IAAI;AAC9B,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,4BAA4B,CAAC;AAC5D,MAAM,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC;AAC9C,MAAM,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAM,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,4BAA4B,CAAC;AAC5D,MAAM,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC;AAC9C,MAAM,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAM,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvF,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACrG,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}