{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/Sidebar/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { SidebarTheme } from \"./Sidebar\";\n\nexport const sidebarTheme = createTheme<SidebarTheme>({\n  root: {\n    base: \"h-full\",\n    collapsed: {\n      on: \"w-16\",\n      off: \"w-64\",\n    },\n    inner: \"h-full overflow-y-auto overflow-x-hidden rounded bg-gray-50 px-3 py-4 dark:bg-gray-800\",\n  },\n  collapse: {\n    button:\n      \"group flex w-full items-center rounded-lg p-2 text-base font-normal text-gray-900 transition duration-75 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700\",\n    icon: {\n      base: \"h-6 w-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white\",\n      open: {\n        off: \"\",\n        on: \"text-gray-900\",\n      },\n    },\n    label: {\n      base: \"ml-3 flex-1 whitespace-nowrap text-left\",\n      title: \"sr-only\",\n      icon: {\n        base: \"h-6 w-6 transition delay-0 ease-in-out\",\n        open: {\n          on: \"rotate-180\",\n          off: \"\",\n        },\n      },\n    },\n    list: \"space-y-2 py-2\",\n  },\n  cta: {\n    base: \"mt-6 rounded-lg bg-gray-100 p-4 dark:bg-gray-700\",\n    color: {\n      blue: \"bg-cyan-50 dark:bg-cyan-900\",\n      dark: \"bg-dark-50 dark:bg-dark-900\",\n      failure: \"bg-red-50 dark:bg-red-900\",\n      gray: \"bg-gray-50 dark:bg-gray-900\",\n      green: \"bg-green-50 dark:bg-green-900\",\n      light: \"bg-light-50 dark:bg-light-900\",\n      red: \"bg-red-50 dark:bg-red-900\",\n      purple: \"bg-purple-50 dark:bg-purple-900\",\n      success: \"bg-green-50 dark:bg-green-900\",\n      yellow: \"bg-yellow-50 dark:bg-yellow-900\",\n      warning: \"bg-yellow-50 dark:bg-yellow-900\",\n    },\n  },\n  item: {\n    base: \"flex items-center justify-center rounded-lg p-2 text-base font-normal text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700\",\n    active: \"bg-gray-100 dark:bg-gray-700\",\n    collapsed: {\n      insideCollapse: \"group w-full pl-8 transition duration-75\",\n      noIcon: \"font-bold\",\n    },\n    content: {\n      base: \"flex-1 whitespace-nowrap px-3\",\n    },\n    icon: {\n      base: \"h-6 w-6 shrink-0 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white\",\n      active: \"text-gray-700 dark:text-gray-100\",\n    },\n    label: \"\",\n    listItem: \"\",\n  },\n  items: {\n    base: \"\",\n  },\n  itemGroup: {\n    base: \"mt-4 space-y-2 border-t border-gray-200 pt-4 first:mt-0 first:border-t-0 first:pt-0 dark:border-gray-700\",\n  },\n  logo: {\n    base: \"mb-5 flex items-center pl-2.5\",\n    collapsed: {\n      on: \"hidden\",\n      off: \"self-center whitespace-nowrap text-xl font-semibold dark:text-white\",\n    },\n    img: \"mr-3 h-6 sm:h-7\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,YAAY,GAAGA,uBAAW,CAAC;AACxC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,SAAS,EAAE;AACf,MAAM,EAAE,EAAE,MAAM;AAChB,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,KAAK,EAAE;AACX,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM,EAAE,mKAAmK;AAC/K,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,uHAAuH;AACnI,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,yCAAyC;AACrD,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,wCAAwC;AACtD,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,YAAY;AAC1B,UAAU,GAAG,EAAE;AACf;AACA;AACA,KAAK;AACL,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,kDAAkD;AAC5D,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,OAAO,EAAE,2BAA2B;AAC1C,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,KAAK,EAAE,+BAA+B;AAC5C,MAAM,KAAK,EAAE,+BAA+B;AAC5C,MAAM,GAAG,EAAE,2BAA2B;AACtC,MAAM,MAAM,EAAE,iCAAiC;AAC/C,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,MAAM,EAAE,iCAAiC;AAC/C,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,8IAA8I;AACxJ,IAAI,MAAM,EAAE,8BAA8B;AAC1C,IAAI,SAAS,EAAE;AACf,MAAM,cAAc,EAAE,0CAA0C;AAChE,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gIAAgI;AAC5I,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,+BAA+B;AACzC,IAAI,SAAS,EAAE;AACf,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,GAAG,EAAE;AACT;AACA,CAAC;;;;"}