import * as recast from "recast";
/**
 * Modifies the given content by adding a value to a specified path within the content.
 *
 * @param {Object} options - Options for adding a value to config.
 * @param {string} options.content - The content to modify.
 * @param {string} options.targetPath - The dot-separated path where the value will be added.
 * @param {Function} options.valueGenerator - Function that generates the AST node to be added.
 * @returns {string} The modified content with the added value.
 */
export declare function addToConfig({ content, targetPath, valueGenerator, }: {
    content: string;
    targetPath: string;
    valueGenerator: (b: typeof recast.types.builders) => types.namedTypes.Expression;
}): string;
