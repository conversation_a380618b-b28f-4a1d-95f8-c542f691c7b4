{"version": 3, "file": "tailwind-merge.cjs", "sources": ["../../src/helpers/tailwind-merge.ts"], "sourcesContent": ["import { extendTailwindMerge as extendTailwindMerge_v2 } from \"tailwind-merge-v2\";\nimport { extendTailwindMerge as extendTailwindMerge_v3, type ClassNameValue } from \"tailwind-merge-v3\";\nimport { getPrefix } from \"../store\";\nimport { getTailwindVersion } from \"./get-tailwind-version\";\n\nconst cache = new Map<string | undefined, ReturnType<typeof extendTailwindMerge_v3>>();\n\nexport function twMerge(...classLists: ClassNameValue[]): string {\n  const prefix = getPrefix();\n  const version = getTailwindVersion();\n\n  const cacheKey = `${prefix}.${version}`;\n  const cacheValue = cache.get(cacheKey);\n\n  if (cacheValue) {\n    return cacheValue(...classLists);\n  }\n\n  const twMergeFn = (version === 3 ? extendTailwindMerge_v2 : extendTailwindMerge_v3)({\n    extend: {\n      classGroups: {\n        \"bg-image\": [\"bg-arrow-down-icon\", \"bg-check-icon\", \"bg-dash-icon\", \"bg-dot-icon\"],\n        shadow: [\"shadow-sm-light\"],\n      },\n    },\n    prefix,\n  });\n  cache.set(cacheKey, twMergeFn);\n\n  return twMergeFn(...classLists);\n}\n"], "names": ["getPrefix", "getTailwindVersion", "extendTailwindMerge_v2", "extendTailwindMerge_v3"], "mappings": ";;;;;;;AAKA,MAAM,KAAK,mBAAmB,IAAI,GAAG,EAAE;AAChC,SAAS,OAAO,CAAC,GAAG,UAAU,EAAE;AACvC,EAAE,MAAM,MAAM,GAAGA,eAAS,EAAE;AAC5B,EAAE,MAAM,OAAO,GAAGC,qCAAkB,EAAE;AACtC,EAAE,MAAM,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACzC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AACxC,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,OAAO,UAAU,CAAC,GAAG,UAAU,CAAC;AACpC;AACA,EAAE,MAAM,SAAS,GAAG,CAAC,OAAO,KAAK,CAAC,GAAGC,mCAAsB,GAAGC,mCAAsB,EAAE;AACtF,IAAI,MAAM,EAAE;AACZ,MAAM,WAAW,EAAE;AACnB,QAAQ,UAAU,EAAE,CAAC,oBAAoB,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,CAAC;AAC1F,QAAQ,MAAM,EAAE,CAAC,iBAAiB;AAClC;AACA,KAAK;AACL,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC;AAChC,EAAE,OAAO,SAAS,CAAC,GAAG,UAAU,CAAC;AACjC;;;;"}