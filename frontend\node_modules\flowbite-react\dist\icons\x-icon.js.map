{"version": 3, "file": "x-icon.js", "sources": ["../../src/icons/x-icon.tsx"], "sourcesContent": ["import { forwardRef, type SVGProps } from \"react\";\n\nexport const XIcon = forwardRef<SVGSVGElement, SVGProps<SVGSVGElement>>((props, ref) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"1em\"\n    height=\"1em\"\n    fill=\"currentColor\"\n    stroke=\"currentColor\"\n    strokeWidth={0}\n    viewBox=\"0 0 20 20\"\n    ref={ref}\n    {...props}\n  >\n    <path\n      fillRule=\"evenodd\"\n      stroke=\"none\"\n      d=\"M4.293 4.293a1 1 0 0 1 1.414 0L10 8.586l4.293-4.293a1 1 0 1 1 1.414 1.414L11.414 10l4.293 4.293a1 1 0 0 1-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 0 1-1.414-1.414L8.586 10 4.293 5.707a1 1 0 0 1 0-1.414z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n));\nXIcon.displayName = \"XIcon\";\n"], "names": [], "mappings": ";;;AAGY,MAAC,KAAK,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,qBAAqB,GAAG;AACnE,EAAE,KAAK;AACP,EAAE;AACF,IAAI,KAAK,EAAE,4BAA4B;AACvC,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,GAAG;AACP,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,kBAAkB,GAAG;AACjC,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,CAAC,EAAE,4MAA4M;AACvN,QAAQ,QAAQ,EAAE;AAClB;AACA;AACA;AACA,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}