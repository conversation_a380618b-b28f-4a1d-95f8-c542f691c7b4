{"version": 3, "file": "TabItem.cjs", "sources": ["../../../src/components/Tabs/TabItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC, type ReactNode } from \"react\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useThemeProvider } from \"../../theme\";\n\nexport interface TabItemProps extends Omit<ComponentProps<\"div\">, \"title\"> {\n  active?: boolean;\n  disabled?: boolean;\n  icon?: FC<ComponentProps<\"svg\">>;\n  title: ReactNode;\n}\n\nexport const TabItem = forwardRef<HTMLDivElement, TabItemProps>((props, ref) => {\n  const provider = useThemeProvider();\n\n  const { title: _, ...restProps } = resolveProps(props, provider.props?.tabItem);\n\n  return <div ref={ref} {...restProps} />;\n});\n\nTabItem.displayName = \"TabItem\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "resolveProps", "jsx"], "mappings": ";;;;;;;;;;AAMY,MAAC,OAAO,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAClD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEF,UAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AACjF,EAAE,uBAAuBG,cAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;AAC1D,CAAC;AACD,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}