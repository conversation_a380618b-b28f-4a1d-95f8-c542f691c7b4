{"version": 3, "file": "TableCell.cjs", "sources": ["../../../src/components/Table/TableCell.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableBodyContext } from \"./TableBodyContext\";\nimport { useTableContext } from \"./TableContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableCellTheme {\n  base: string;\n}\n\nexport interface TableCellProps extends ComponentPropsWithRef<\"td\">, ThemingProps<TableCellTheme> {}\n\nexport const TableCell = forwardRef<HTMLTableCellElement, TableCellProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTableContext();\n  const { theme: bodyTheme, clearTheme: bodyClearTheme, applyTheme: bodyApplyTheme } = useTableBodyContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.body.cell, provider.theme?.table?.body?.cell, rootTheme?.body?.cell, bodyTheme?.cell, props.theme],\n    [\n      get(provider.clearTheme, \"table.body.cell\"),\n      get(rootClearTheme, \"body.cell\"),\n      get(bodyClearTheme, \"cell\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"table.body.cell\"),\n      get(rootApplyTheme, \"body.cell\"),\n      get(bodyApplyTheme, \"cell\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableCell);\n\n  return <td ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTableCell.displayName = \"TableCell\";\n"], "names": ["forwardRef", "useTableContext", "useTableBodyContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "tableTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,SAAS,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,4BAAe,EAAE;AACxG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,oCAAmB,EAAE;AAC5G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,IAAI,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClH,IAAI;AACJ,MAAMK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAMK,OAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAMA,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAMK,OAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAMA,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuBO,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACpG,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}