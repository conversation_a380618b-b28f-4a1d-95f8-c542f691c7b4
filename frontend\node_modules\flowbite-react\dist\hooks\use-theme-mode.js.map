{"version": 3, "file": "use-theme-mode.js", "sources": ["../../src/hooks/use-theme-mode.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { isClient } from \"../helpers/is-client\";\nimport { useWatchLocalStorageValue } from \"../hooks/use-watch-localstorage-value\";\nimport { getMode, getPrefix } from \"../store\";\n\nconst DEFAULT_MODE: ThemeMode = \"auto\";\nconst LS_THEME_MODE = \"flowbite-theme-mode\";\nconst SYNC_THEME_MODE = \"flowbite-theme-mode-sync\";\n\nexport type ThemeMode = \"light\" | \"dark\" | \"auto\";\n\nexport function useThemeMode() {\n  const [mode, setMode] = useState<ThemeMode>(getInitialMode(getMode()));\n\n  /**\n   * Sync all tabs with the latest theme mode value\n   */\n  useWatchLocalStorageValue({\n    key: LS_THEME_MODE,\n    onChange(newMode: ThemeMode | null) {\n      setMode(validateMode(newMode ?? DEFAULT_MODE));\n    },\n  });\n\n  /**\n   * Keep the other instances of the hook in sync (bi-directional)\n   */\n  useSyncMode((mode) => setMode(mode));\n\n  /**\n   * Sets `mode` to a given value: `light | dark` | `auto`\n   */\n  function handleSetMode(mode: ThemeMode) {\n    setMode(mode);\n    setModeInLS(mode);\n    setModeInDOM(mode);\n    document.dispatchEvent(new CustomEvent(SYNC_THEME_MODE, { detail: mode }));\n  }\n\n  /**\n   * Toggles between: `light | dark`\n   */\n  function toggleMode() {\n    let newMode = mode;\n\n    if (newMode === \"auto\") {\n      newMode = computeModeValue(newMode);\n    }\n\n    newMode = newMode === \"dark\" ? \"light\" : \"dark\";\n\n    handleSetMode(newMode);\n  }\n\n  /**\n   * Clears the mode\n   */\n  function clearMode() {\n    const newMode = mode ?? DEFAULT_MODE;\n\n    handleSetMode(newMode);\n  }\n\n  return {\n    mode,\n    computedMode: computeModeValue(mode),\n    setMode: handleSetMode,\n    toggleMode,\n    clearMode,\n  };\n}\n\n/**\n * Custom event listener on `SYNC_THEME_MODE`\n */\nfunction useSyncMode(onChange: (mode: ThemeMode) => void) {\n  useEffect(() => {\n    function handleSync(e: Event) {\n      const mode = (e as CustomEvent<ThemeMode>).detail;\n      onChange(mode);\n    }\n\n    document.addEventListener(SYNC_THEME_MODE, handleSync);\n    return () => document.removeEventListener(SYNC_THEME_MODE, handleSync);\n  }, []);\n}\n\n/**\n * Sets the give value in local storage\n */\nfunction setModeInLS(mode: ThemeMode) {\n  localStorage.setItem(LS_THEME_MODE, mode);\n}\n\n/**\n * Add or remove class `dark` on `html` element\n */\nfunction setModeInDOM(mode: ThemeMode) {\n  const prefix = getPrefix() ?? \"\";\n  const computedMode = computeModeValue(mode);\n\n  if (computedMode === \"dark\") {\n    document.documentElement.classList.add(`${prefix}dark`);\n  } else {\n    document.documentElement.classList.remove(`${prefix}dark`);\n  }\n}\n\nfunction getInitialMode(defaultMode?: ThemeMode): ThemeMode {\n  if (!isClient()) {\n    return DEFAULT_MODE;\n  }\n\n  const storageMode = localStorage.getItem(LS_THEME_MODE) as ThemeMode | null;\n\n  return validateMode(storageMode ?? defaultMode ?? DEFAULT_MODE);\n}\n\n/**\n * Parse `auto` mode value to either `light` or `dark`\n * @returns `light` | `dark`\n */\nfunction computeModeValue(mode: ThemeMode): ThemeMode {\n  if (!isClient()) {\n    return DEFAULT_MODE;\n  }\n\n  return mode === \"auto\" ? prefersColorScheme() : mode;\n}\n\n/**\n * Get browser prefered color scheme\n * @returns `light` | `dark`\n */\nfunction prefersColorScheme(): ThemeMode {\n  return window.matchMedia?.(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n}\n\n/**\n * Validate the mode value\n * @param mode - The mode value to validate\n * @returns `light` | `dark` | `auto`\n */\nfunction validateMode(mode: ThemeMode): ThemeMode {\n  if ([\"light\", \"dark\", \"auto\"].includes(mode)) {\n    return mode;\n  }\n  return DEFAULT_MODE;\n}\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,YAAY,GAAG,MAAM;AAC3B,MAAM,aAAa,GAAG,qBAAqB;AAC3C,MAAM,eAAe,GAAG,0BAA0B;AAC3C,SAAS,YAAY,GAAG;AAC/B,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7D,EAAE,yBAAyB,CAAC;AAC5B,IAAI,GAAG,EAAE,aAAa;AACtB,IAAI,QAAQ,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,CAAC,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,CAAC;AACpD;AACA,GAAG,CAAC;AACJ,EAAE,WAAW,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB,IAAI,WAAW,CAAC,KAAK,CAAC;AACtB,IAAI,YAAY,CAAC,KAAK,CAAC;AACvB,IAAI,QAAQ,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/E;AACA,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,OAAO,GAAG,IAAI;AACtB,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACzC;AACA,IAAI,OAAO,GAAG,OAAO,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;AACnD,IAAI,aAAa,CAAC,OAAO,CAAC;AAC1B;AACA,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,MAAM,OAAO,GAAG,IAAI,IAAI,YAAY;AACxC,IAAI,aAAa,CAAC,OAAO,CAAC;AAC1B;AACA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,YAAY,EAAE,gBAAgB,CAAC,IAAI,CAAC;AACxC,IAAI,OAAO,EAAE,aAAa;AAC1B,IAAI,UAAU;AACd,IAAI;AACJ,GAAG;AACH;AACA,SAAS,WAAW,CAAC,QAAQ,EAAE;AAC/B,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,SAAS,UAAU,CAAC,CAAC,EAAE;AAC3B,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM;AAC3B,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpB;AACA,IAAI,QAAQ,CAAC,gBAAgB,CAAC,eAAe,EAAE,UAAU,CAAC;AAC1D,IAAI,OAAO,MAAM,QAAQ,CAAC,mBAAmB,CAAC,eAAe,EAAE,UAAU,CAAC;AAC1E,GAAG,EAAE,EAAE,CAAC;AACR;AACA,SAAS,WAAW,CAAC,IAAI,EAAE;AAC3B,EAAE,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;AAC3C;AACA,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,MAAM,MAAM,GAAG,SAAS,EAAE,IAAI,EAAE;AAClC,EAAE,MAAM,YAAY,GAAG,gBAAgB,CAAC,IAAI,CAAC;AAC7C,EAAE,IAAI,YAAY,KAAK,MAAM,EAAE;AAC/B,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,GAAG,MAAM;AACT,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D;AACA;AACA,SAAS,cAAc,CAAC,WAAW,EAAE;AACrC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE;AACnB,IAAI,OAAO,YAAY;AACvB;AACA,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC;AACzD,EAAE,OAAO,YAAY,CAAC,WAAW,IAAI,WAAW,IAAI,YAAY,CAAC;AACjE;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE;AACnB,IAAI,OAAO,YAAY;AACvB;AACA,EAAE,OAAO,IAAI,KAAK,MAAM,GAAG,kBAAkB,EAAE,GAAG,IAAI;AACtD;AACA,SAAS,kBAAkB,GAAG;AAC9B,EAAE,OAAO,MAAM,CAAC,UAAU,GAAG,8BAA8B,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO;AACvF;AACA,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAChD,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,YAAY;AACrB;;;;"}