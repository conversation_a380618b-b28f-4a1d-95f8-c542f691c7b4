{"version": 3, "file": "theme.js", "sources": ["../../../src/components/ToggleSwitch/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { ToggleSwitchTheme } from \"./ToggleSwitch\";\n\nexport const toggleSwitchTheme = createTheme<ToggleSwitchTheme>({\n  root: {\n    base: \"group flex rounded-lg focus:outline-none\",\n    active: {\n      on: \"cursor-pointer\",\n      off: \"cursor-not-allowed opacity-50\",\n    },\n    label: \"ms-3 mt-0.5 text-start text-sm font-medium text-gray-900 dark:text-gray-300\",\n    input: \"sr-only\",\n  },\n  toggle: {\n    base: \"relative rounded-full after:absolute after:rounded-full after:border after:bg-white after:transition-all group-focus:ring-4\",\n    checked: {\n      on: \"after:translate-x-full after:border-transparent rtl:after:-translate-x-full\",\n      off: \"bg-gray-200 after:border-gray-300 dark:bg-gray-700\",\n      color: {\n        default: \"bg-primary-700 group-focus:ring-primary-300 dark:group-focus:ring-primary-800\",\n        blue: \"bg-blue-700 group-focus:ring-blue-300 dark:group-focus:ring-blue-800\",\n        dark: \"bg-gray-700 group-focus:ring-gray-300 dark:group-focus:ring-gray-800\",\n        failure: \"bg-red-700 group-focus:ring-red-300 dark:group-focus:ring-red-800\",\n        gray: \"bg-gray-500 group-focus:ring-gray-300 dark:group-focus:ring-gray-800\",\n        green: \"bg-green-600 group-focus:ring-green-300 dark:group-focus:ring-green-800\",\n        light: \"bg-gray-300 group-focus:ring-gray-300 dark:group-focus:ring-gray-800\",\n        red: \"bg-red-700 group-focus:ring-red-300 dark:group-focus:ring-red-800\",\n        purple: \"bg-purple-700 group-focus:ring-purple-300 dark:group-focus:ring-purple-800\",\n        success: \"bg-green-500 group-focus:ring-green-300 dark:group-focus:ring-green-800\",\n        yellow: \"bg-yellow-400 group-focus:ring-yellow-300 dark:group-focus:ring-yellow-800\",\n        warning: \"bg-yellow-600 group-focus:ring-yellow-300 dark:group-focus:ring-yellow-800\",\n        cyan: \"bg-cyan-500 group-focus:ring-cyan-300 dark:group-focus:ring-cyan-800\",\n        lime: \"bg-lime-400 group-focus:ring-lime-300 dark:group-focus:ring-lime-800\",\n        indigo: \"bg-indigo-400 group-focus:ring-indigo-300 dark:group-focus:ring-indigo-800\",\n        teal: \"bg-teal-400 group-focus:ring-teal-300 dark:group-focus:ring-teal-800\",\n        info: \"bg-cyan-600 group-focus:ring-cyan-300 dark:group-focus:ring-cyan-800\",\n        pink: \"bg-pink-600 group-focus:ring-pink-300 dark:group-focus:ring-pink-800\",\n      },\n    },\n    sizes: {\n      sm: \"h-5 w-9 min-w-9 after:left-0.5 after:top-0.5 after:h-4 after:w-4 rtl:after:right-0.5\",\n      md: \"h-6 w-11 min-w-11 after:left-0.5 after:top-0.5 after:h-5 after:w-5 rtl:after:right-0.5\",\n      lg: \"h-7 w-[52px] min-w-[52px] after:left-0.5 after:top-0.5 after:h-6 after:w-6 rtl:after:right-0.5\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,iBAAiB,GAAG,WAAW,CAAC;AAC7C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,0CAA0C;AACpD,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,gBAAgB;AAC1B,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,KAAK,EAAE,6EAA6E;AACxF,IAAI,KAAK,EAAE;AACX,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,6HAA6H;AACvI,IAAI,OAAO,EAAE;AACb,MAAM,EAAE,EAAE,6EAA6E;AACvF,MAAM,GAAG,EAAE,oDAAoD;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,OAAO,EAAE,+EAA+E;AAChG,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,OAAO,EAAE,mEAAmE;AACpF,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,KAAK,EAAE,yEAAyE;AACxF,QAAQ,KAAK,EAAE,sEAAsE;AACrF,QAAQ,GAAG,EAAE,mEAAmE;AAChF,QAAQ,MAAM,EAAE,4EAA4E;AAC5F,QAAQ,OAAO,EAAE,yEAAyE;AAC1F,QAAQ,MAAM,EAAE,4EAA4E;AAC5F,QAAQ,OAAO,EAAE,4EAA4E;AAC7F,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,MAAM,EAAE,4EAA4E;AAC5F,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,IAAI,EAAE,sEAAsE;AACpF,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,sFAAsF;AAChG,MAAM,EAAE,EAAE,wFAAwF;AAClG,MAAM,EAAE,EAAE;AACV;AACA;AACA,CAAC;;;;"}